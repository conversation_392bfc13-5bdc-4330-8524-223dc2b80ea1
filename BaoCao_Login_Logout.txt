================================================================================
                    BÁO CÁO CHI TIẾT CHỨC NĂNG LOGIN & LOGOUT
                        AI CHATBOT FOR HOSPITAL SYSTEM
================================================================================

================================================================================
                                🔐 BACKEND (FastAPI)
================================================================================

📍 ĐƯỜNG DẪN CHÍNH:
   File: backend\app\auth\routes.py
   
🔗 API ENDPOINTS:
   ✅ POST /api/v1/auth/login     - Xử lý đăng nhập người dùng
      • Nhận: {username: "admin", password: "123456"}
      • Trả về: {access_token: "eyJ0eXAi...", token_type: "bearer", user: {...}}
      • Mục đích: Xác thực thông tin đăng nhập và tạo JWT token

   ✅ POST /api/v1/auth/logout    - Xử lý đăng xuất người dùng
      • Nhận: Không cần body (chỉ cần gọi endpoint)
      • Trả về: {type: "success", message: "Logout successful"}
      • Mục đích: Thông báo server user đã logout (client tự xóa token)

   ✅ GET  /api/v1/auth/me        - Lấy thông tin user hiện tại
      • Nhận: Bearer token trong header Authorization
      • Trả về: {username: "admin", role: "doctor", full_name: "Nguyễn Văn A", department_name: "Khoa Tim"}
      • Mục đích: Lấy thông tin chi tiết của user đang đăng nhập

   ✅ GET  /api/v1/auth/login     - Serve trang login HTML
      • Nhận: Không cần gì
      • Trả về: File HTML của trang login
      • Mục đích: Phục vụ trang login tĩnh (nếu không dùng React)

🛡️ CẤU HÌNH BẢO MẬT:

   🔑 SECRET KEY: "your-secret-key-change-in-production" ⚠️ CẦN THAY ĐỔI
      • Là gì: Chuỗi bí mật dùng để ký và xác minh JWT token
      • Tại sao quan trọng: Nếu bị lộ, hacker có thể tạo token giả mạo
      • Ví dụ: Như chìa khóa để mở khóa - ai có chìa khóa đều vào được nhà
      • Khuyến nghị: Dùng chuỗi ngẫu nhiên dài 32+ ký tự trong production

   🔐 ALGORITHM: HS256 (HMAC with SHA-256)
      • Là gì: Thuật toán mã hóa để tạo chữ ký số cho JWT token
      • Cách hoạt động: Kết hợp secret key + dữ liệu → tạo chữ ký không thể giả mạo
      • Tại sao dùng: Đảm bảo token không bị sửa đổi trong quá trình truyền

   ⏰ TOKEN EXPIRY: 30 phút
      • Là gì: Thời gian token có hiệu lực
      • Tại sao có hạn: Giảm rủi ro nếu token bị đánh cắp
      • Sau 30 phút: Token tự động vô hiệu, user phải đăng nhập lại
      • Cân bằng: Ngắn = bảo mật cao nhưng phiền user, Dài = tiện nhưng rủi ro

   🔒 PASSWORD HASHING: bcrypt với CryptContext
      • Là gì: Mã hóa mật khẩu trước khi lưu database
      • Tại sao không lưu mật khẩu gốc: Nếu database bị hack, mật khẩu vẫn an toàn
      • bcrypt đặc biệt: Chậm có chủ ý để chống brute force attack
      • Ví dụ: "123456" → "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW"

   🛡️ AUTHENTICATION: HTTPBearer (JWT Token)
      • Là gì: Cách gửi token trong HTTP request
      • Format: Header "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
      • Cách hoạt động: Mỗi request đến API cần có token này để xác minh danh tính

📋 PYDANTIC MODELS:
   1. LoginRequest:
      - username: str (tên đăng nhập)
      - password: str (mật khẩu)
   
   2. Token:
      - access_token: str (JWT token)
      - token_type: str (loại token = "bearer")
   
   3. UserResponse:
      - username: str (tên đăng nhập)
      - role: str (vai trò người dùng)
      - full_name: str (họ tên đầy đủ)
      - department_name: str (tên phòng ban)

🔧 HÀM CHÍNH:

   1. verify_password(plain_password, hashed_password):
      - Xác minh mật khẩu với hash đã lưu
      - Sử dụng bcrypt để so sánh
   
   2. get_password_hash(password):
      - Mã hóa mật khẩu thành hash bcrypt
   
   3. create_access_token(data, expires_delta):
      - Tạo JWT token với thời hạn
      - Encode dữ liệu user vào token
   
   4. get_current_user(credentials):
      - Giải mã JWT token
      - Lấy thông tin user từ database
      - Xử lý lỗi authentication

📊 LUỒNG XỬ LÝ LOGIN:
   1. Nhận LoginRequest (username, password)
   2. Tìm user trong database theo username
   3. Kiểm tra mật khẩu với bcrypt
   4. Tạo JWT token với thời hạn 30 phút
   5. Trả về response với token và thông tin user

📊 LUỒNG XỬ LÝ LOGOUT:
   1. Nhận POST request đến /logout
   2. Trả về message "Logout successful"
   3. Client tự xóa token khỏi localStorage

================================================================================
                                🎨 FRONTEND (React)
================================================================================

📁 CẤU TRÚC FILES:
   • frontend\src\pages\Login.jsx              - Trang đăng nhập
   • frontend\src\contexts\AuthContext.jsx     - Context quản lý auth
   • frontend\src\components\Auth\ProtectedRoute.jsx - Bảo vệ routes
   • frontend\src\components\Layout\Header.jsx - Header với nút logout
   • frontend\src\App.jsx                      - Routing chính

🔐 AUTHCONTEXT (Quản lý trạng thái):
   
   State Variables:
   • user: Object chứa thông tin user (username, name, role, department)
   • loading: Boolean cho trạng thái loading
   • isAuthenticated: Boolean kiểm tra đã đăng nhập

   Methods:
   • login(username, password):
     - Gửi POST request đến /api/v1/auth/login
     - Lưu user data và token vào localStorage
     - Cập nhật state user
     - Trả về {success: boolean, error?: string}
   
   • logout():
     - Gửi POST request đến /api/v1/auth/logout
     - Xóa user và token khỏi localStorage
     - Reset state user về null

🖥️ LOGIN PAGE (Login.jsx):
   
   Features:
   • Form validation với useState
   • Error handling và hiển thị lỗi
   • Loading state khi đang xử lý
   • Auto-redirect nếu đã đăng nhập
   • Dark/Light theme support
   • Responsive design

   Form Fields:
   • Username input với icon user
   • Password input với icon lock và toggle visibility
   • Submit button với loading spinner

🛡️ PROTECTED ROUTE (ProtectedRoute.jsx):
   
   Logic:
   • Kiểm tra loading state → hiển thị LoadingSpinner
   • Kiểm tra isAuthenticated → redirect đến /login nếu chưa đăng nhập
   • Render children nếu đã authenticated

📱 HEADER COMPONENT (Header.jsx):
   
   Features:
   • Hiển thị tên hệ thống
   • Thông tin user hiện tại
   • Nút toggle dark/light theme
   • Dropdown menu với nút logout

🗺️ ROUTING (App.jsx):
   
   Route Structure:
   • /login - Public route (Login page)
   • / - Protected route (redirect to /chatbot)
   • /dashboard - Protected route
   • /chatbot - Protected route
   • /patient-search - Protected route
   • /consultation-search - Protected route
   • /doctor-search - Protected route
   • /settings - Protected route
   • /* - Catch all (redirect to /chatbot)

================================================================================
                            🔄 LUỒNG HOẠT ĐỘNG CHI TIẾT
================================================================================

🔑 LUỒNG ĐĂNG NHẬP:
   1. User truy cập trang login (/login)
   2. Nhập username và password
   3. Click "Login" → gọi AuthContext.login()
   4. Frontend gửi POST /api/v1/auth/login với credentials
   5. Backend xác thực user và tạo JWT token
   6. Frontend nhận response với token và user data
   7. Lưu token và user vào localStorage
   8. Cập nhật AuthContext state
   9. Auto-redirect đến /chatbot

🚪 LUỒNG ĐĂNG XUẤT:
   1. User click nút logout trong header
   2. Gọi AuthContext.logout()
   3. Gửi POST /api/v1/auth/logout (optional)
   4. Xóa token và user khỏi localStorage
   5. Reset AuthContext state
   6. Auto-redirect đến /login

🔒 LUỒNG BẢO VỆ ROUTE:
   1. User truy cập protected route
   2. ProtectedRoute component kiểm tra isAuthenticated
   3. Nếu chưa đăng nhập → redirect đến /login
   4. Nếu đã đăng nhập → render component

🔄 LUỒNG KHÔI PHỤC SESSION:
   1. User refresh trang hoặc mở lại browser
   2. AuthContext useEffect chạy
   3. Kiểm tra localStorage có user và token
   4. Nếu có → restore state
   5. Nếu không → giữ state null

================================================================================
                                    KẾT LUẬN
================================================================================

Hệ thống Login/Logout đã được triển khai hoàn chỉnh với các tính năng cơ bản:
- Authentication bằng JWT token
- Bảo mật mật khẩu với bcrypt
- Giao diện thân thiện và responsive
- Protected routes và session management

