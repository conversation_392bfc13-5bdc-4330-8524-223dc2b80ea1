================================================================================
                    BÁO CÁO CHI TIẾT CHỨC NĂNG LOGIN & LOGOUT
                        AI CHATBOT FOR HOSPITAL SYSTEM
================================================================================

================================================================================
                                🔐 BACKEND (FastAPI)
================================================================================

📍 ĐƯỜNG DẪN CHÍNH:
   File: backend\app\auth\routes.py
   
🔗 API ENDPOINTS (Giống như cửa ra vào của tòa nhà):

   🚪 POST /api/v1/auth/login - CỬA VÀO CHÍNH
      • Giống như: Bảo vệ kiểm tra thẻ căn cước ở cổng
      • Bạn đưa: Tên + mật khẩu
      • Bảo vệ trả: Thẻ ra vào (token) + thông tin cá nhân
      • Ví dụ: "Tôi là bác sĩ Nam, mật khẩu 123" → Nhận thẻ bác sĩ

   🚪 POST /api/v1/auth/logout - CỬA RA
      • Giống như: Trả thẻ ra vào khi về
      • Bạn nói: "Tôi muốn ra về"
      • Bảo vệ nói: "OK, chào anh"
      • Kết quả: Thẻ không còn hiệu lực

   🆔 GET /api/v1/auth/me - KIỂM TRA THÔNG TIN
      • Giống như: Hỏi "Tôi là ai, làm gì?"
      • Bạn đưa: Thẻ ra vào
      • Hệ thống trả: "Anh là bác sĩ Nam, khoa Tim mạch"
      • Mục đích: Xem thông tin bản thân

   📄 GET /api/v1/auth/login - TRANG ĐĂNG NHẬP
      • Giống như: Bảng hướng dẫn ở cổng
      • Hiển thị: Form điền tên + mật khẩu
      • Cho ai: Người chưa có thẻ ra vào

🛡️ CẤU HÌNH BẢO MẬT (Giống như hệ thống an ninh tòa nhà):

   🔑 SECRET KEY - CHÌA KHÓA TỔNG
      • Giống như: Chìa khóa tổng của tòa nhà
      • Hiện tại: "your-secret-key-change-in-production" (Giống để chìa khóa ở dưới thảm!)
      • Nguy hiểm: Ai biết chìa khóa này đều có thể làm thẻ giả
      • Cần làm: Đổi thành chìa khóa phức tạp, chỉ chủ tòa nhà biết
      • Ví dụ tốt: "ToaNha_BenhVien_2024_ChiaKhoa_BaoMat_XYZ789"

   🔐 ALGORITHM HS256 - MÁY LÀM THẺ
      • Giống như: Máy in thẻ ra vào có con dấu đặc biệt
      • Cách hoạt động: Dùng chìa khóa tổng + thông tin người → tạo thẻ không thể giả
      • Tại sao an toàn: Không có chìa khóa tổng thì không làm được thẻ giống

   ⏰ TOKEN EXPIRY - THẺ CÓ HẠN SỬ DỤNG
      • Giống như: Thẻ ra vào chỉ dùng được 30 phút
      • Tại sao: Nếu ai đó nhặt được thẻ rơi, chỉ dùng được 30 phút thôi
      • Sau 30 phút: Thẻ tự động hết hạn, phải xin thẻ mới
      • Cân bằng: 30 phút vừa đủ làm việc, không quá lâu nếu mất thẻ

   🔒 PASSWORD HASHING - MÃ HÓA MẬT KHẨU
      • Giống như: Thay vì ghi "mật khẩu: 123456", ghi "mật khẩu: #@$%^&*"
      • Tại sao: Nếu kẻ xấu đột nhập database, chỉ thấy ký tự lạ
      • Đặc biệt: Cùng mật khẩu nhưng mỗi lần mã hóa ra kết quả khác
      • Ví dụ: "123456" → "$2b$12$abc..." (không thể đoán ngược)

   🛡️ BEARER TOKEN - THẺ RA VÀO
      • Giống như: Mỗi lần vào phòng phải đưa thẻ
      • Cách dùng: Gắn thẻ vào đầu mỗi yêu cầu "Authorization: Bearer [thẻ]"
      • Bảo vệ kiểm tra: Thẻ có hợp lệ không? Còn hạn không? → Cho vào hoặc từ chối

📋 PYDANTIC MODELS:
   1. LoginRequest:
      - username: str (tên đăng nhập)
      - password: str (mật khẩu)
   
   2. Token:
      - access_token: str (JWT token)
      - token_type: str (loại token = "bearer")
   
   3. UserResponse:
      - username: str (tên đăng nhập)
      - role: str (vai trò người dùng)
      - full_name: str (họ tên đầy đủ)
      - department_name: str (tên phòng ban)

🔧 HÀM CHÍNH:

   1. verify_password(plain_password, hashed_password):
      - Xác minh mật khẩu với hash đã lưu
      - Sử dụng bcrypt để so sánh
   
   2. get_password_hash(password):
      - Mã hóa mật khẩu thành hash bcrypt
   
   3. create_access_token(data, expires_delta):
      - Tạo JWT token với thời hạn
      - Encode dữ liệu user vào token
   
   4. get_current_user(credentials):
      - Giải mã JWT token
      - Lấy thông tin user từ database
      - Xử lý lỗi authentication

📊 LUỒNG XỬ LÝ LOGIN:
   1. Nhận LoginRequest (username, password)
   2. Tìm user trong database theo username
   3. Kiểm tra mật khẩu với bcrypt
   4. Tạo JWT token với thời hạn 30 phút
   5. Trả về response với token và thông tin user

📊 LUỒNG XỬ LÝ LOGOUT:
   1. Nhận POST request đến /logout
   2. Trả về message "Logout successful"
   3. Client tự xóa token khỏi localStorage

================================================================================
                                🎨 FRONTEND (React)
================================================================================

📁 CẤU TRÚC FILES:
   • frontend\src\pages\Login.jsx              - Trang đăng nhập
   • frontend\src\contexts\AuthContext.jsx     - Context quản lý auth
   • frontend\src\components\Auth\ProtectedRoute.jsx - Bảo vệ routes
   • frontend\src\components\Layout\Header.jsx - Header với nút logout
   • frontend\src\App.jsx                      - Routing chính

🔐 AUTHCONTEXT (Quản lý trạng thái):
   
   State Variables:
   • user: Object chứa thông tin user (username, name, role, department)
   • loading: Boolean cho trạng thái loading
   • isAuthenticated: Boolean kiểm tra đã đăng nhập

   Methods:
   • login(username, password):
     - Gửi POST request đến /api/v1/auth/login
     - Lưu user data và token vào localStorage
     - Cập nhật state user
     - Trả về {success: boolean, error?: string}
   
   • logout():
     - Gửi POST request đến /api/v1/auth/logout
     - Xóa user và token khỏi localStorage
     - Reset state user về null

🖥️ LOGIN PAGE (Login.jsx):
   
   Features:
   • Form validation với useState
   • Error handling và hiển thị lỗi
   • Loading state khi đang xử lý
   • Auto-redirect nếu đã đăng nhập
   • Dark/Light theme support
   • Responsive design

   Form Fields:
   • Username input với icon user
   • Password input với icon lock và toggle visibility
   • Submit button với loading spinner

🛡️ PROTECTED ROUTE (ProtectedRoute.jsx):
   
   Logic:
   • Kiểm tra loading state → hiển thị LoadingSpinner
   • Kiểm tra isAuthenticated → redirect đến /login nếu chưa đăng nhập
   • Render children nếu đã authenticated

📱 HEADER COMPONENT (Header.jsx):
   
   Features:
   • Hiển thị tên hệ thống
   • Thông tin user hiện tại
   • Nút toggle dark/light theme
   • Dropdown menu với nút logout

🗺️ ROUTING (App.jsx):
   
   Route Structure:
   • /login - Public route (Login page)
   • / - Protected route (redirect to /chatbot)
   • /dashboard - Protected route
   • /chatbot - Protected route
   • /patient-search - Protected route
   • /consultation-search - Protected route
   • /doctor-search - Protected route
   • /settings - Protected route
   • /* - Catch all (redirect to /chatbot)

================================================================================
                            🔄 LUỒNG HOẠT ĐỘNG CHI TIẾT
================================================================================

🔑 LUỒNG ĐĂNG NHẬP:
   1. User truy cập trang login (/login)
   2. Nhập username và password
   3. Click "Login" → gọi AuthContext.login()
   4. Frontend gửi POST /api/v1/auth/login với credentials
   5. Backend xác thực user và tạo JWT token
   6. Frontend nhận response với token và user data
   7. Lưu token và user vào localStorage
   8. Cập nhật AuthContext state
   9. Auto-redirect đến /chatbot

🚪 LUỒNG ĐĂNG XUẤT:
   1. User click nút logout trong header
   2. Gọi AuthContext.logout()
   3. Gửi POST /api/v1/auth/logout (optional)
   4. Xóa token và user khỏi localStorage
   5. Reset AuthContext state
   6. Auto-redirect đến /login

🔒 LUỒNG BẢO VỆ ROUTE:
   1. User truy cập protected route
   2. ProtectedRoute component kiểm tra isAuthenticated
   3. Nếu chưa đăng nhập → redirect đến /login
   4. Nếu đã đăng nhập → render component

🔄 LUỒNG KHÔI PHỤC SESSION:
   1. User refresh trang hoặc mở lại browser
   2. AuthContext useEffect chạy
   3. Kiểm tra localStorage có user và token
   4. Nếu có → restore state
   5. Nếu không → giữ state null

================================================================================
                                    KẾT LUẬN
================================================================================

Hệ thống Login/Logout đã được triển khai hoàn chỉnh với các tính năng cơ bản:
- Authentication bằng JWT token
- Bảo mật mật khẩu với bcrypt
- Giao diện thân thiện và responsive
- Protected routes và session management

