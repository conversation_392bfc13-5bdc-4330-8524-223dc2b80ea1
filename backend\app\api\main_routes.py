import logging
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import FileResponse

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/health")
async def health_check(request: Request):
    """Health check endpoint for monitoring"""
    vn = request.app.state.vn

    try:
        # Check database connection
        vn.run_sql("SELECT 1")
        return {"status": "healthy", "message": "Service is operational"}
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {"status": "unhealthy", "message": str(e)}

@router.get("/version")
async def get_api_version():
    """Get API version information"""
    return {
        "api_version": "2.0.0",
        "current_version": "v1",
        "supported_versions": ["v1"],
        "version_info": {
            "v1": {
                "status": "stable",
                "description": "Current stable API version - all endpoints use /api/v1/ prefix",
                "base_path": "/api/v1",
                "endpoints": {
                    "health": "/api/v1/health",
                    "auth": "/api/v1/auth/",
                    "sql": "/api/v1/sql/",
                    "analysis": "/api/v1/analysis/",
                    "dashboard": "/api/v1/dashboard/",
                    "search": "/api/v1/search/"
                }
            }
        },
        "notes": {
            "migration_complete": "All legacy v0 endpoints have been removed",
            "single_version": "API now uses single version strategy with v1 endpoints only"
        }
    }

@router.get("/endpoints")
async def get_all_endpoints():
    """Get comprehensive list of all API endpoints"""
    return {
        "api_documentation": {
            "title": "Hospital Assistant System API",
            "version": "2.0.0",
            "description": "Complete endpoint reference - V1 endpoints only"
        },
        "endpoints": {
            "base_path": "/api/v1",
            "health": {
                "GET /api/v1/health": "Health check endpoint",
                "GET /api/v1/version": "API version information",
                "GET /api/v1/endpoints": "Complete endpoint documentation"
            },
            "authentication": {
                "POST /api/v1/auth/login": "User login",
                "POST /api/v1/auth/logout": "User logout",
                "GET /api/v1/auth/me": "Get current user information"
            },
            "sql": {
                "GET /api/v1/sql/generate_sql": "Generate SQL from natural language",
                "GET /api/v1/sql/run_sql": "Execute SQL query"
            },
            "analysis": {
                "GET /api/v1/analysis/generate_summary_stream": "Generate streaming data summary",
                "GET /api/v1/analysis/generate_plotly_figure": "Generate chart visualization"
            },
            "dashboard": {
                "GET /api/v1/dashboard/test": "Dashboard API test endpoint",
                "GET /api/v1/dashboard/patients": "Patient statistics",
                "GET /api/v1/dashboard/consultations": "Consultation statistics",
                "GET /api/v1/dashboard/doctors": "Doctor statistics",
                "GET /api/v1/dashboard/patient-demographics": "Patient demographics"
            },
            "search": {
                "POST /api/v1/search/patients": "Search patients",
                "POST /api/v1/search/consultations": "Search consultations",
                "POST /api/v1/search/doctors": "Search doctors",
                "GET /api/v1/search/patients/{patient_id}/details": "Get patient details",
                "GET /api/v1/search/consultations/{consultation_id}/details": "Get consultation details",
                "GET /api/v1/search/doctors/{doctor_id}/details": "Get doctor details",
                "GET /api/v1/search/test": "Search API test endpoint"
            }
        },
        "notes": {
            "version_strategy": "Single version API - all endpoints use /api/v1/ prefix",
            "migration_complete": "Legacy v0 endpoints have been removed",
            "development": "All development uses v1 endpoints for consistency"
        }
    }

@router.get("/static/{filename:path}")
async def serve_static(filename: str):
    """Serve static files"""
    try:
        return FileResponse(f"static/{filename}")
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="File not found")
