-- Department table
CREATE TABLE department (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT
);

-- Role table
CREATE TABLE role (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT
);

-- Permission table
CREATE TABLE permission (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT
);

-- Role-Permission mapping
CREATE TABLE role_permission (
    id SERIAL PRIMARY KEY,
    role_id INTEGER REFERENCES role(id),
    permission_id INTEGER REFERENCES permission(id)
);

-- User table
CREATE TABLE "user" (
    id SERIAL PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    password_hash TEXT NOT NULL,
    department_id INTEGER REFERENCES department(id),
    role_id INTEGER REFERENCES role(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now(),
    last_login TIMESTAMP NULL
);

-- Conversation/session table for multiturn chat
CREATE TABLE conversation (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES "user"(id),
    started_at TIMESTAMP DEFAULT now(),
    last_activity TIMESTAMP DEFAULT now() -- updated on each message or action
);

-- Topic table (used for LLM label and topic)
CREATE TABLE topic (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT
);

-- Chat history table, now linked to conversation and topic
-- Enhanced to support rich message types including charts, thinking processes, and intermediate SQL
CREATE TABLE chat_history (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES "user"(id),
    conversation_id INTEGER REFERENCES conversation(id),

    -- Message structure
    message_type TEXT NOT NULL CHECK (message_type IN ('user', 'assistant')),
    content_type TEXT NOT NULL CHECK (content_type IN ('text', 'sql', 'chart', 'thinking', 'intermediate_sql', 'error')),
    content TEXT, -- Main message content

    -- SQL-related data
    sql_query TEXT, -- SQL query if applicable
    table_data JSONB, -- Table results data
    chart_data JSONB, -- Chart/plotly data

    -- Metadata and UI control
    metadata JSONB, -- Additional data like querySessionId, iteration, timestamps, UI flags
    is_hidden BOOLEAN DEFAULT FALSE, -- For UI visibility control
    parent_message_id INTEGER REFERENCES chat_history(id), -- For linking related messages

    -- Timestamps and references
    created_at TIMESTAMP DEFAULT now(),
    topic_id INTEGER REFERENCES topic(id),
);

-- Feedback table, linked to chat_history and user
CREATE TABLE feedback (
    id SERIAL PRIMARY KEY,
    chat_history_id INTEGER REFERENCES chat_history(id),
    user_id INTEGER REFERENCES "user"(id),
    is_liked BOOLEAN,
    created_at TIMESTAMP DEFAULT now()
);

-- Audit log table
-- details JSONB example: {
--   "ip_address": "string",
--   "user_agent": "string",
--   "query_time_ms": number,    -- time taken for query/response
--   "result_size": number,      -- number of rows or bytes returned
--   "error_message": "string",  -- error details if any
--   "endpoint": "string",       -- API endpoint accessed
--   "extra": { ... }            -- any additional context
-- }
CREATE TABLE audit_log (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES "user"(id),
    action TEXT NOT NULL,
    details JSONB,
    timestamp TIMESTAMP DEFAULT now()
);

-- Indexes for performance optimization
-- User table indexes
CREATE INDEX idx_user_department_id ON "user"(department_id);
CREATE INDEX idx_user_role_id ON "user"(role_id);
CREATE INDEX idx_user_last_login ON "user"(last_login);
-- Helps: Find active users ordered by last login (e.g., admin dashboard)
CREATE INDEX idx_user_active_login ON "user"(is_active, last_login DESC);

-- Conversation indexes
CREATE INDEX idx_conversation_last_activity ON conversation(last_activity);
-- Helps: Fetch a user's most recent conversations (e.g., chat sidebar)
CREATE INDEX idx_conversation_user_activity ON conversation(user_id, last_activity DESC);

-- Chat history indexes
CREATE INDEX idx_chat_history_user_id ON chat_history(user_id);
CREATE INDEX idx_chat_history_topic_id ON chat_history(topic_id);
CREATE INDEX idx_chat_history_created_at ON chat_history(created_at);
-- Helps: Retrieve all messages in a conversation in order (chat window)
CREATE INDEX idx_chat_history_conversation_created ON chat_history(conversation_id, created_at);
-- New indexes for enhanced chat functionality
CREATE INDEX idx_chat_history_message_type ON chat_history(message_type);
CREATE INDEX idx_chat_history_content_type ON chat_history(content_type);
CREATE INDEX idx_chat_history_parent_message ON chat_history(parent_message_id);
CREATE INDEX idx_chat_history_is_hidden ON chat_history(is_hidden);
-- Helps: Find messages by conversation and type (e.g., all SQL messages in a conversation)
CREATE INDEX idx_chat_history_conversation_type ON chat_history(conversation_id, content_type, created_at);
-- Helps: Find visible messages in a conversation
CREATE INDEX idx_chat_history_conversation_visible ON chat_history(conversation_id, is_hidden, created_at);
-- Helps: Efficiently query metadata JSONB field
CREATE INDEX idx_chat_history_metadata_gin ON chat_history USING gin(metadata);

-- Feedback indexes
CREATE INDEX idx_feedback_chat_history_id ON feedback(chat_history_id);
CREATE INDEX idx_feedback_user_id ON feedback(user_id);
-- Helps: Quickly find all positive feedback for analytics
CREATE INDEX idx_feedback_liked ON feedback(chat_history_id) WHERE is_liked = true;
-- Helps: Ensure a user can only give one feedback per chat message
CREATE UNIQUE INDEX idx_feedback_unique ON feedback(chat_history_id, user_id);

-- Audit log indexes
CREATE INDEX idx_audit_log_action ON audit_log(action);
CREATE INDEX idx_audit_log_timestamp ON audit_log(timestamp);
-- Helps: Retrieve a user's audit logs in reverse chronological order
CREATE INDEX idx_audit_log_user_timestamp ON audit_log(user_id, timestamp DESC);
-- Helps: Efficiently query/filter on JSONB details (e.g., by IP, endpoint)
CREATE INDEX idx_audit_log_details_gin ON audit_log USING gin(details);

-- Role permission unique constraint
-- Helps: Prevent duplicate role-permission assignments
CREATE UNIQUE INDEX idx_role_permission_unique ON role_permission(role_id, permission_id);

-- Chat History Schema Documentation
-- ================================
-- The enhanced chat_history table supports rich message types for the chat application:
--
-- Message Types:
-- - 'user': User questions/input
-- - 'assistant': AI assistant responses
--
-- Content Types:
-- - 'text': Regular text messages
-- - 'sql': SQL queries and results
-- - 'chart': Chart/visualization data
-- - 'thinking': AI thinking process (hidden from user by default)
-- - 'intermediate_sql': Intermediate SQL steps in multi-step queries
-- - 'error': Error messages and failed operations
--
-- Data Storage:
-- - content: Main text content of the message
-- - sql_query: SQL query text (for sql and intermediate_sql types)
-- - table_data: JSONB containing query results/table data
-- - chart_data: JSONB containing Plotly chart configuration
-- - metadata: JSONB for additional data like:
--   * querySessionId: Unique ID for tracking related messages
--   * iteration: Step number for intermediate SQL processes
--   * sqlTimestamp: When SQL was executed
--   * isLoading, isThinking, isStreaming: UI state flags
--   * showSql, hideAssistantMessage: UI visibility controls
--
-- Relationships:
-- - parent_message_id: Links related messages (e.g., intermediate steps to main query)
-- - conversation_id: Groups messages into conversations
-- - is_hidden: Controls UI visibility (e.g., hide intermediate steps after completion)
--
-- Backward Compatibility:
-- - question/answer fields maintained for existing data
-- - New messages use the enhanced structure
 